<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Onnyx Platform{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='images/favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="48x48" href="{{ url_for('static', filename='images/favicon-48x48.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">
    <meta name="theme-color" content="#00fff7">

    <!-- Google Fonts - Orbitron & Montserrat -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS with custom config -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'orbitron': ['Orbitron', 'monospace'],
                        'montserrat': ['Montserrat', 'sans-serif'],
                    },
                    colors: {
                        'onyx': {
                            'black': '#1a1a1a',    // Brightened for better accessibility
                            'gray': '#2a2a2a',     // Improved contrast base
                            'light': '#3a3a3a',    // Lighter accent areas
                        },
                        'cyber': {
                            'cyan': '#00fff7',     // Enhanced with glow effects
                            'purple': '#9a00ff',   // Boosted vibrancy
                            'blue': '#0066ff',     // Increased visibility
                        },
                        'text': {
                            'primary': '#eeeeee',     // Brighter primary text
                            'secondary': '#d1d5db',   // Improved secondary text (gray-300)
                            'tertiary': '#9ca3af',    // Enhanced tertiary text (gray-400)
                            'muted': '#6b7280',       // Muted text (gray-500)
                        }
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}?v=compact-fix-2024">

    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- QR Code library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Alpine.js for interactivity -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    {% block head %}{% endblock %}
</head>
<body class="bg-onyx-black text-text-primary font-montserrat overflow-x-hidden"
      style="min-height: 100vh; display: flex; flex-direction: column; margin: 0; padding: 0;">
    <!-- Professional Navigation Bar -->
    <nav class="onnyx-navbar">
        <div class="navbar-container">
            <!-- Logo Section -->
            <div class="navbar-logo">
                <a href="{{ url_for('index') }}" class="logo-link">
                    <div class="logo-icon">
                        <span class="logo-symbol">⬢</span>
                    </div>
                    <span class="logo-text">ONNYX</span>
                </a>
            </div>

            <!-- Main Navigation Links -->
            <div class="navbar-nav">
                <a href="{{ url_for('index') }}" class="nav-item">
                    <span class="nav-icon">🏠</span>
                    <span class="nav-label">Home</span>
                </a>
                <a href="{{ url_for('sela.directory') }}" class="nav-item">
                    <span class="nav-icon">🏢</span>
                    <span class="nav-label">Validators</span>
                </a>
                <a href="{{ url_for('explorer.index') }}" class="nav-item">
                    <span class="nav-icon">🔍</span>
                    <span class="nav-label">Explorer</span>
                </a>
                <a href="{{ url_for('tokenomics.overview') }}" class="nav-item nav-item-featured">
                    <span class="nav-icon">📜</span>
                    <span class="nav-label">Biblical Tokenomics</span>
                    <span class="nav-badge">New</span>
                </a>
                {% if current_user %}
                <a href="{{ url_for('dashboard.overview') }}" class="nav-item">
                    <span class="nav-icon">📊</span>
                    <span class="nav-label">Dashboard</span>
                </a>
                <a href="{{ url_for('auto_mining.dashboard') }}" class="nav-item">
                    <span class="nav-icon">⚡</span>
                    <span class="nav-label">Auto-Mining</span>
                </a>
                {% endif %}
            </div>

            <!-- Mobile Menu Toggle -->
            <div class="mobile-menu-toggle">
                <button class="mobile-menu-btn">
                    <div class="hamburger">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </button>

                <!-- Mobile Menu Overlay -->
                <div class="mobile-menu-overlay" style="display: none;">
                    <div class="mobile-menu-content">
                        <!-- Navigation Section -->
                        <div class="mobile-nav-section">
                            <div class="mobile-nav-title">Navigation</div>
                            <a href="{{ url_for('index') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">🏠</span>
                                <span>Home</span>
                            </a>
                            <a href="{{ url_for('sela.directory') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">🏢</span>
                                <span>Validators</span>
                            </a>
                            <a href="{{ url_for('explorer.index') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">🔍</span>
                                <span>Explorer</span>
                            </a>
                            <a href="{{ url_for('tokenomics.overview') }}" class="mobile-nav-item featured">
                                <span class="mobile-nav-icon">📜</span>
                                <span>Biblical Tokenomics</span>
                            </a>
                            {% if current_user %}
                            <a href="{{ url_for('dashboard.overview') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">📊</span>
                                <span>Dashboard</span>
                            </a>
                            <a href="{{ url_for('auto_mining.dashboard') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">⚡</span>
                                <span>Auto-Mining</span>
                            </a>
                            {% endif %}
                        </div>

                        <!-- Authentication Section -->
                        {% if current_user %}
                        <div class="mobile-nav-section">
                            <div class="mobile-nav-title">Account</div>
                            <a href="{{ url_for('tokenomics.dashboard') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">📜</span>
                                <span>Biblical Tokenomics</span>
                            </a>
                            <a href="{{ url_for('dashboard.profile') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">⚙️</span>
                                <span>Profile Settings</span>
                            </a>
                            <a href="{{ url_for('dashboard.identity_details') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">👤</span>
                                <span>My Identity</span>
                            </a>
                            <a href="{{ url_for('auth.logout') }}" class="mobile-nav-item">
                                <span class="mobile-nav-icon">🚪</span>
                                <span>Logout</span>
                            </a>
                        </div>
                        {% else %}
                        <div class="mobile-nav-section">
                            <div class="mobile-nav-title">Get Started</div>
                            <div class="mobile-auth-buttons">
                                <a href="{{ url_for('auth.login') }}" class="mobile-auth-btn secondary">
                                    <span class="btn-icon">🔑</span>
                                    <span>Access Portal</span>
                                </a>
                                <a href="{{ url_for('register_choice') }}" class="mobile-auth-btn primary">
                                    <span class="btn-icon">✨</span>
                                    <span>Verify Identity</span>
                                </a>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Authentication Section -->
            <div class="navbar-auth">
                {% if current_user %}
                    <!-- User Profile Dropdown -->
                    <div class="user-dropdown">
                        <button class="user-button">
                            <div class="user-avatar">
                                <span class="user-initial">{{ current_user.name[0].upper() }}</span>
                            </div>
                            <div class="user-details">
                                <span class="user-name">{{ current_user.name }}</span>
                                <span class="user-status">Online</span>
                            </div>
                            <svg class="dropdown-chevron" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div class="user-menu" style="display: none;">
                            <div class="menu-header">
                                <div class="menu-user-info">
                                    <div class="menu-avatar">
                                        <span>{{ current_user.name[0].upper() }}</span>
                                    </div>
                                    <div>
                                        <div class="menu-name">{{ current_user.name }}</div>
                                        <div class="menu-email">{{ current_user.email or 'No email' }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="menu-divider"></div>
                            <a href="{{ url_for('dashboard.overview') }}" class="menu-item">
                                <span class="menu-icon">📊</span>
                                <span>Dashboard</span>
                            </a>
                            <a href="{{ url_for('tokenomics.dashboard') }}" class="menu-item">
                                <span class="menu-icon">📜</span>
                                <span>Biblical Tokenomics</span>
                            </a>
                            <a href="{{ url_for('dashboard.profile') }}" class="menu-item">
                                <span class="menu-icon">⚙️</span>
                                <span>Profile Settings</span>
                            </a>
                            <a href="{{ url_for('dashboard.identity_details') }}" class="menu-item">
                                <span class="menu-icon">👤</span>
                                <span>My Identity</span>
                            </a>
                            <div class="menu-divider"></div>
                            <a href="{{ url_for('auth.logout') }}" class="menu-item menu-item-danger">
                                <span class="menu-icon">🚪</span>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                {% else %}
                    <!-- Guest Authentication Buttons -->
                    <div class="auth-buttons">
                        <a href="{{ url_for('auth.login') }}" class="auth-btn auth-btn-secondary">
                            <span class="btn-icon">🔑</span>
                            <span>Access Portal</span>
                        </a>
                        <a href="{{ url_for('register_choice') }}" class="auth-btn auth-btn-primary">
                            <span class="btn-icon">✨</span>
                            <span>Verify Identity</span>
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content Wrapper - Flex container that expands to push footer down -->
    <div style="flex: 1 0 auto; display: flex; flex-direction: column; min-height: calc(100vh - 64px);">
        <!-- Content area with top padding for fixed nav -->
        <main style="flex: 1 0 auto; padding-top: 4rem; padding-bottom: 4rem; width: 100%; position: relative;">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                    {% for category, message in messages %}
                    <div class="glass-card mb-4 p-4 rounded-xl border {% if category == 'error' %}border-red-500/30 bg-red-500/10 text-red-300{% elif category == 'success' %}border-green-500/30 bg-green-500/10 text-green-300{% elif category == 'warning' %}border-yellow-500/30 bg-yellow-500/10 text-yellow-300{% else %}border-cyber-cyan/30 bg-cyber-cyan/10 text-cyber-cyan{% endif %} backdrop-blur-sm">
                        <div class="flex items-center space-x-3">
                            {% if category == 'error' %}
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% elif category == 'success' %}
                            <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% elif category == 'warning' %}
                            <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            {% else %}
                            <svg class="w-5 h-5 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% endif %}
                            <span class="font-medium">{{ message }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            {% endwith %}

            <!-- Page Content -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Unified Footer - Consistent Across All Pages -->
    <footer class="footer-responsive" style="flex-shrink: 0; margin-top: auto; position: relative; width: 100%; z-index: 10;">
        <div class="footer-grid">
            <!-- ONNYX Platform Section -->
            <div class="footer-section">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-14 h-14 rounded-xl flex items-center justify-center shadow-lg shadow-cyber-cyan/30 bg-white/8 backdrop-blur-sm border border-white/15">
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             class="w-12 h-12 object-contain"
                             style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                    </div>
                    <div>
                        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan">ONNYX</h3>
                        <p class="text-sm text-gray-300 font-medium">Trustworthy Commerce</p>
                    </div>
                </div>
                <p class="text-gray-300 text-sm leading-relaxed mb-6">
                    The Digital Backbone of Trustworthy Commerce. Blockchain-powered verification platform enabling transparent business operations through cryptographic identity management and decentralized validation networks.
                </p>
                <div class="footer-network-status">
                    <div class="footer-network-indicator">
                        <div class="footer-network-dot"></div>
                        <span>Network Online</span>
                    </div>
                    <div class="footer-network-indicator">
                        <span class="font-mono text-xs">v1.0.0</span>
                    </div>
                </div>
            </div>

            <!-- Platform Navigation Section -->
            <div class="footer-section">
                <h3>Platform</h3>
                <ul class="space-y-1">
                    <li><a href="{{ url_for('sela.directory') }}" class="footer-link">
                        <span class="mr-3">🏢</span><span>Validator Network</span>
                    </a></li>
                    <li><a href="{{ url_for('explorer.index') }}" class="footer-link">
                        <span class="mr-3">🔍</span><span>Blockchain Explorer</span>
                    </a></li>
                    <li><a href="{{ url_for('register_choice') }}" class="footer-link">
                        <span class="mr-3">🔐</span><span>Identity Verification</span>
                    </a></li>
                    <li><a href="#" class="footer-link">
                        <span class="mr-3">⚡</span><span>Auto-Mining</span>
                    </a></li>
                </ul>
            </div>

            <!-- Network Statistics Section -->
            <div class="footer-section">
                <h3>Network Stats</h3>
                <ul class="space-y-1">
                    <li class="footer-stat-item">
                        <span class="stat-label">Verified Identities</span>
                        <span class="stat-value">{{ platform_stats.identities }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Active Validators</span>
                        <span class="stat-value">{{ platform_stats.selas }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Total Transactions</span>
                        <span class="stat-value">{{ platform_stats.transactions }}</span>
                    </li>
                    <li class="footer-stat-item">
                        <span class="stat-label">Blocks Secured</span>
                        <span class="stat-value">{{ platform_stats.blocks }}</span>
                    </li>
                </ul>
            </div>

            <!-- Technology Stack Section -->
            <div class="footer-section">
                <h3>Technology</h3>
                <ul class="space-y-1">
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-cyan"></div>
                        <span class="footer-tech-label">Quantum-Resistant Security</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-purple"></div>
                        <span class="footer-tech-label">Etzem Trust Protocol</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-cyber-blue"></div>
                        <span class="footer-tech-label">Mikvah Token Economy</span>
                    </li>
                    <li class="footer-tech-item">
                        <div class="footer-tech-dot bg-green-400"></div>
                        <span class="footer-tech-label">Decentralized Validation</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Footer Bottom Section -->
        <div class="footer-bottom">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <p>&copy; 2024 ONNYX Platform. Securing the future of digital commerce.</p>
                <div class="flex items-center space-x-6 text-sm">
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span>Network Online</span>
                    </span>
                    <span class="font-mono">v1.0.0</span>
                    <span class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                        <span>Blockchain Active</span>
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <!-- Enhanced Scroll Effects -->
    <script src="{{ url_for('static', filename='js/scroll-effects.js') }}"></script>
    <!-- Navigation Testing Script (Development Only) -->
    {% if config.DEBUG %}
    <script src="{{ url_for('static', filename='js/navigation-test.js') }}"></script>
    <script src="{{ url_for('static', filename='js/navigation-verification.js') }}"></script>
    <script src="{{ url_for('static', filename='js/navigation-fix-verification.js') }}"></script>
    {% endif %}

    <!-- Navigation Functionality Script -->
    <script>
        // Enhanced navigation functionality with fallbacks
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 ONNYX Navigation System Initializing...');

            // Mobile Menu Functionality
            initializeMobileMenu();

            // User Dropdown Functionality
            initializeUserDropdown();

            // Navigation Verification
            verifyNavigationState();
        });

        function initializeMobileMenu() {
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const mobileBtn = document.querySelector('.mobile-menu-btn');
            const mobileOverlay = document.querySelector('.mobile-menu-overlay');
            const hamburger = document.querySelector('.hamburger');

            if (!mobileBtn || !mobileOverlay) {
                console.warn('⚠️ Mobile menu elements not found');
                return;
            }

            let isOpen = false;

            // Mobile menu toggle function
            function toggleMobileMenu() {
                isOpen = !isOpen;

                if (isOpen) {
                    mobileOverlay.style.display = 'block';
                    mobileOverlay.style.opacity = '0';

                    // Force reflow
                    mobileOverlay.offsetHeight;

                    mobileOverlay.style.transition = 'opacity 0.3s ease';
                    mobileOverlay.style.opacity = '1';

                    if (hamburger) {
                        hamburger.classList.add('active');
                    }

                    document.body.style.overflow = 'hidden';
                } else {
                    mobileOverlay.style.transition = 'opacity 0.3s ease';
                    mobileOverlay.style.opacity = '0';

                    setTimeout(() => {
                        mobileOverlay.style.display = 'none';
                    }, 300);

                    if (hamburger) {
                        hamburger.classList.remove('active');
                    }

                    document.body.style.overflow = '';
                }

                console.log(`📱 Mobile menu ${isOpen ? 'opened' : 'closed'}`);
            }

            // Click handler for mobile button
            mobileBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleMobileMenu();
            });

            // Close menu when clicking outside
            mobileOverlay.addEventListener('click', function(e) {
                if (e.target === mobileOverlay) {
                    toggleMobileMenu();
                }
            });

            // Close menu when clicking nav items
            const mobileNavItems = mobileOverlay.querySelectorAll('.mobile-nav-item, .mobile-auth-btn');
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    setTimeout(() => toggleMobileMenu(), 100);
                });
            });

            // Close on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && isOpen) {
                    toggleMobileMenu();
                }
            });

            console.log('✅ Mobile menu initialized');
        }

        function initializeUserDropdown() {
            const userButton = document.querySelector('.user-button');
            const userMenu = document.querySelector('.user-menu');
            const chevron = document.querySelector('.dropdown-chevron');

            if (!userButton || !userMenu) {
                console.log('ℹ️ User dropdown not present (guest user)');
                return;
            }

            let isOpen = false;

            function toggleUserDropdown() {
                isOpen = !isOpen;

                if (isOpen) {
                    userMenu.style.display = 'block';
                    userMenu.style.opacity = '0';
                    userMenu.style.transform = 'scale(0.95)';

                    // Force reflow
                    userMenu.offsetHeight;

                    userMenu.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
                    userMenu.style.opacity = '1';
                    userMenu.style.transform = 'scale(1)';

                    if (chevron) {
                        chevron.style.transform = 'rotate(180deg)';
                    }
                } else {
                    userMenu.style.transition = 'opacity 0.15s ease, transform 0.15s ease';
                    userMenu.style.opacity = '0';
                    userMenu.style.transform = 'scale(0.95)';

                    setTimeout(() => {
                        userMenu.style.display = 'none';
                    }, 150);

                    if (chevron) {
                        chevron.style.transform = 'rotate(0deg)';
                    }
                }

                console.log(`👤 User dropdown ${isOpen ? 'opened' : 'closed'}`);
            }

            userButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleUserDropdown();
            });

            // Close when clicking outside
            document.addEventListener('click', function(e) {
                if (!userButton.contains(e.target) && !userMenu.contains(e.target) && isOpen) {
                    toggleUserDropdown();
                }
            });

            // Close on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && isOpen) {
                    toggleUserDropdown();
                }
            });

            console.log('✅ User dropdown initialized');
        }

        function verifyNavigationState() {
            const width = window.innerWidth;
            const navbar = document.querySelector('.navbar-nav');
            const mobileToggle = document.querySelector('.mobile-menu-toggle');

            console.log(`📏 Viewport: ${width}px`);

            if (width <= 768) {
                const navHidden = !navbar || window.getComputedStyle(navbar).display === 'none';
                const mobileVisible = mobileToggle && window.getComputedStyle(mobileToggle).display !== 'none';

                console.log(`📱 Mobile mode: Nav hidden: ${navHidden}, Mobile toggle visible: ${mobileVisible}`);
            } else {
                const navVisible = navbar && window.getComputedStyle(navbar).display !== 'none';
                const mobileHidden = !mobileToggle || window.getComputedStyle(mobileToggle).display === 'none';

                console.log(`🖥️ Desktop mode: Nav visible: ${navVisible}, Mobile toggle hidden: ${mobileHidden}`);
            }
        }

        // Monitor viewport changes
        window.addEventListener('resize', function() {
            setTimeout(verifyNavigationState, 100);
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
