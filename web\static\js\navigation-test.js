/**
 * ONNYX Navigation Testing Script
 * Comprehensive testing for responsive navigation across all breakpoints
 */

class NavigationTester {
    constructor() {
        this.breakpoints = {
            'Extra Small Mobile': 320,
            'Small Mobile': 480,
            'Mobile': 768,
            'Tablet': 992,
            'Desktop': 1200,
            'Large Desktop': 1400
        };
        
        this.testResults = {};
        this.init();
    }

    init() {
        console.log('🚀 ONNYX Navigation Testing Suite Initialized');
        this.createTestControls();
        this.runInitialTests();
    }

    createTestControls() {
        // Create floating test panel
        const testPanel = document.createElement('div');
        testPanel.id = 'nav-test-panel';
        testPanel.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            width: 300px;
            background: rgba(26, 26, 26, 0.95);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1rem;
            z-index: 10000;
            font-family: 'Montserrat', sans-serif;
            font-size: 0.8rem;
            color: #00ffff;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        `;

        testPanel.innerHTML = `
            <h3 style="margin: 0 0 1rem 0; color: #00ffff; font-size: 1rem;">📱 Navigation Tester</h3>
            <div id="test-controls">
                ${Object.entries(this.breakpoints).map(([name, width]) => 
                    `<button onclick="navTester.testBreakpoint(${width}, '${name}')" 
                             style="display: block; width: 100%; margin: 0.25rem 0; padding: 0.5rem; 
                                    background: rgba(0, 255, 255, 0.1); border: 1px solid rgba(0, 255, 255, 0.3); 
                                    color: #00ffff; border-radius: 6px; cursor: pointer; font-size: 0.75rem;">
                        ${name} (${width}px)
                    </button>`
                ).join('')}
            </div>
            <div id="test-results" style="margin-top: 1rem; max-height: 200px; overflow-y: auto;"></div>
            <button onclick="navTester.runAllTests()" 
                    style="width: 100%; margin-top: 1rem; padding: 0.75rem; 
                           background: linear-gradient(135deg, #00ffff, #8b5cf6); 
                           border: none; color: black; border-radius: 8px; 
                           font-weight: bold; cursor: pointer;">
                🧪 Run All Tests
            </button>
        `;

        document.body.appendChild(testPanel);
    }

    testBreakpoint(width, name) {
        console.log(`🔍 Testing ${name} breakpoint (${width}px)`);
        
        // Resize viewport
        window.resizeTo(width + 100, 800);
        
        setTimeout(() => {
            const results = this.analyzeNavigation(width, name);
            this.displayResults(name, results);
            this.testResults[name] = results;
        }, 500);
    }

    analyzeNavigation(width, breakpointName) {
        const results = {
            breakpoint: breakpointName,
            width: width,
            tests: {},
            issues: [],
            passed: 0,
            total: 0
        };

        // Test 1: Navigation visibility
        const navbar = document.querySelector('.navbar-nav');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        
        if (width <= 768) {
            results.tests.mobileMenuVisible = mobileToggle && window.getComputedStyle(mobileToggle).display !== 'none';
            results.tests.desktopNavHidden = navbar && window.getComputedStyle(navbar).display === 'none';
        } else {
            results.tests.desktopNavVisible = navbar && window.getComputedStyle(navbar).display !== 'none';
            results.tests.mobileMenuHidden = !mobileToggle || window.getComputedStyle(mobileToggle).display === 'none';
        }

        // Test 2: Touch target sizes
        const touchTargets = document.querySelectorAll('.nav-item, .auth-btn, .user-button, .mobile-menu-btn');
        let validTouchTargets = 0;
        
        touchTargets.forEach(target => {
            const rect = target.getBoundingClientRect();
            if (rect.height >= 44 && rect.width >= 44) {
                validTouchTargets++;
            }
        });
        
        results.tests.touchTargetsValid = validTouchTargets === touchTargets.length;

        // Test 3: Biblical Tokenomics prominence
        const featuredItem = document.querySelector('.nav-item-featured, .mobile-nav-item.featured');
        if (featuredItem) {
            const styles = window.getComputedStyle(featuredItem);
            results.tests.biblicalTokenomicsProminent = 
                styles.background.includes('gradient') || 
                styles.borderColor.includes('255, 255') ||
                styles.boxShadow !== 'none';
        }

        // Test 4: Authentication button sizing
        const authBtns = document.querySelectorAll('.auth-btn');
        let properSizedAuthBtns = 0;
        
        authBtns.forEach(btn => {
            const rect = btn.getBoundingClientRect();
            const maxWidth = width <= 480 ? 120 : width <= 768 ? 140 : 160;
            if (rect.width <= maxWidth) {
                properSizedAuthBtns++;
            }
        });
        
        results.tests.authButtonsSized = authBtns.length === 0 || properSizedAuthBtns === authBtns.length;

        // Test 5: Logo scaling
        const logoText = document.querySelector('.logo-text');
        if (logoText) {
            const fontSize = parseFloat(window.getComputedStyle(logoText).fontSize);
            const expectedSize = width <= 320 ? 18 : width <= 480 ? 20 : width <= 768 ? 24 : 28;
            results.tests.logoScaling = Math.abs(fontSize - expectedSize) <= 4;
        }

        // Count passed tests
        Object.values(results.tests).forEach(passed => {
            results.total++;
            if (passed) results.passed++;
        });

        // Generate issues
        Object.entries(results.tests).forEach(([test, passed]) => {
            if (!passed) {
                results.issues.push(this.getIssueDescription(test, breakpointName));
            }
        });

        return results;
    }

    getIssueDescription(test, breakpoint) {
        const descriptions = {
            mobileMenuVisible: `Mobile menu toggle not visible on ${breakpoint}`,
            desktopNavHidden: `Desktop navigation not hidden on ${breakpoint}`,
            desktopNavVisible: `Desktop navigation not visible on ${breakpoint}`,
            mobileMenuHidden: `Mobile menu toggle not hidden on ${breakpoint}`,
            touchTargetsValid: `Touch targets too small on ${breakpoint} (minimum 44px required)`,
            biblicalTokenomicsProminent: `Biblical Tokenomics not prominent enough on ${breakpoint}`,
            authButtonsSized: `Authentication buttons not properly sized on ${breakpoint}`,
            logoScaling: `Logo not scaling correctly on ${breakpoint}`
        };
        
        return descriptions[test] || `Unknown issue with ${test} on ${breakpoint}`;
    }

    displayResults(breakpointName, results) {
        const resultsDiv = document.getElementById('test-results');
        const passRate = ((results.passed / results.total) * 100).toFixed(1);
        const status = passRate >= 80 ? '✅' : passRate >= 60 ? '⚠️' : '❌';
        
        const resultHTML = `
            <div style="margin: 0.5rem 0; padding: 0.5rem; border: 1px solid rgba(0, 255, 255, 0.2); border-radius: 6px;">
                <strong>${status} ${breakpointName}</strong><br>
                <small>Pass Rate: ${passRate}% (${results.passed}/${results.total})</small>
                ${results.issues.length > 0 ? `<br><small style="color: #ff6b6b;">Issues: ${results.issues.length}</small>` : ''}
            </div>
        `;
        
        resultsDiv.innerHTML = resultHTML + resultsDiv.innerHTML;
        
        console.log(`📊 ${breakpointName} Results:`, results);
    }

    runAllTests() {
        console.log('🧪 Running comprehensive navigation tests...');
        
        let testIndex = 0;
        const breakpointEntries = Object.entries(this.breakpoints);
        
        const runNextTest = () => {
            if (testIndex < breakpointEntries.length) {
                const [name, width] = breakpointEntries[testIndex];
                this.testBreakpoint(width, name);
                testIndex++;
                setTimeout(runNextTest, 1000);
            } else {
                this.generateReport();
            }
        };
        
        runNextTest();
    }

    generateReport() {
        console.log('📋 Generating comprehensive test report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            totalBreakpoints: Object.keys(this.testResults).length,
            overallIssues: [],
            recommendations: []
        };

        // Analyze results
        Object.values(this.testResults).forEach(result => {
            report.overallIssues.push(...result.issues);
        });

        // Generate recommendations
        if (report.overallIssues.length === 0) {
            report.recommendations.push('🎉 All navigation tests passed! Excellent responsive design.');
        } else {
            report.recommendations.push('🔧 Review and fix the identified issues for optimal user experience.');
        }

        console.log('📊 Final Navigation Test Report:', report);
        
        // Display summary
        const resultsDiv = document.getElementById('test-results');
        resultsDiv.innerHTML = `
            <div style="margin: 1rem 0; padding: 1rem; background: rgba(0, 255, 255, 0.1); border-radius: 8px;">
                <strong>📊 Test Summary</strong><br>
                <small>Breakpoints Tested: ${report.totalBreakpoints}</small><br>
                <small>Total Issues: ${report.overallIssues.length}</small><br>
                <small style="color: ${report.overallIssues.length === 0 ? '#4ade80' : '#fbbf24'};">
                    ${report.recommendations[0]}
                </small>
            </div>
        ` + resultsDiv.innerHTML;
    }

    runInitialTests() {
        // Run a quick test on current viewport
        const currentWidth = window.innerWidth;
        let breakpointName = 'Unknown';
        
        for (const [name, width] of Object.entries(this.breakpoints)) {
            if (currentWidth <= width) {
                breakpointName = name;
                break;
            }
        }
        
        if (breakpointName === 'Unknown') breakpointName = 'Large Desktop';
        
        setTimeout(() => {
            const results = this.analyzeNavigation(currentWidth, breakpointName);
            this.displayResults(`Current (${breakpointName})`, results);
        }, 1000);
    }
}

// Initialize the navigation tester
const navTester = new NavigationTester();

// Make it globally accessible
window.navTester = navTester;
