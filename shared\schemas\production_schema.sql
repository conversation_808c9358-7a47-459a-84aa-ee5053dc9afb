-- Onnyx Production Database Schema
-- Minimal schema for production readiness

-- Core identities table (no foreign key dependencies)
CREATE TABLE IF NOT EXISTS identities (
    identity_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT,
    public_key TEXT NOT NULL,
    nation_id TEXT,
    metadata TEXT NOT NULL DEFAULT '{}',
    status TEXT NOT NULL DEFAULT 'active',
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_identities_name ON identities(name);
CREATE INDEX IF NOT EXISTS idx_identities_email ON identities(email);

-- Core transactions table
CREATE TABLE IF NOT EXISTS transactions (
    tx_id TEXT PRIMARY KEY,
    block_hash TEXT,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_transactions_sender ON transactions(sender);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON transactions(timestamp);

-- Core blocks table
CREATE TABLE IF NOT EXISTS blocks (
    block_hash TEXT PRIMARY KEY,
    block_height INTEGER NOT NULL UNIQUE,
    previous_hash TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    miner TEXT NOT NULL,
    transactions TEXT NOT NULL DEFAULT '[]',
    merkle_root TEXT NOT NULL,
    nonce INTEGER NOT NULL DEFAULT 0,
    difficulty INTEGER NOT NULL DEFAULT 1,
    created_at INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_blocks_height ON blocks(block_height);
CREATE INDEX IF NOT EXISTS idx_blocks_timestamp ON blocks(timestamp);

-- Mempool table
CREATE TABLE IF NOT EXISTS mempool (
    tx_id TEXT PRIMARY KEY,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    created_at INTEGER NOT NULL
);

-- Tokens table
CREATE TABLE IF NOT EXISTS tokens (
    token_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    creator_id TEXT NOT NULL,
    supply INTEGER NOT NULL DEFAULT 0,
    category TEXT NOT NULL,
    decimals INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    metadata TEXT NOT NULL DEFAULT '{}'
);

CREATE INDEX IF NOT EXISTS idx_tokens_creator ON tokens(creator_id);
CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON tokens(symbol);

-- Token balances table
CREATE TABLE IF NOT EXISTS token_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    balance INTEGER NOT NULL DEFAULT 0,
    updated_at INTEGER NOT NULL,
    UNIQUE(token_id, identity_id)
);

CREATE INDEX IF NOT EXISTS idx_token_balances_token ON token_balances(token_id);
CREATE INDEX IF NOT EXISTS idx_token_balances_identity ON token_balances(identity_id);

-- Sela business registry table (Enhanced for Phase 1 Production)
CREATE TABLE IF NOT EXISTS selas (
    sela_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    address TEXT,
    phone TEXT,
    website TEXT,
    services TEXT,
    stake_amount INTEGER NOT NULL DEFAULT 0,
    stake_token_id TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    trust_score REAL DEFAULT 0.0,
    mining_tier TEXT DEFAULT 'basic',
    mining_power INTEGER DEFAULT 1,
    mining_rewards_earned REAL DEFAULT 0.0,
    blocks_mined INTEGER DEFAULT 0,
    onx_balance REAL DEFAULT 0.0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    metadata TEXT NOT NULL DEFAULT '{}'
);

CREATE INDEX IF NOT EXISTS idx_selas_identity ON selas(identity_id);
CREATE INDEX IF NOT EXISTS idx_selas_status ON selas(status);

-- Activity ledger table
CREATE TABLE IF NOT EXISTS activity_ledger (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    sela_id TEXT,
    activity_type TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',
    timestamp INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_activity_ledger_identity ON activity_ledger(identity_id);
CREATE INDEX IF NOT EXISTS idx_activity_ledger_sela ON activity_ledger(sela_id);
CREATE INDEX IF NOT EXISTS idx_activity_ledger_timestamp ON activity_ledger(timestamp);

-- Event logs table
CREATE TABLE IF NOT EXISTS event_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',
    timestamp INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_event_logs_type ON event_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_event_logs_entity ON event_logs(entity_id);
CREATE INDEX IF NOT EXISTS idx_event_logs_timestamp ON event_logs(timestamp);

-- Chain parameters table
CREATE TABLE IF NOT EXISTS chain_parameters (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    default_value TEXT NOT NULL,
    description TEXT,
    category TEXT,
    last_updated INTEGER,
    metadata TEXT DEFAULT '{}'
);

CREATE INDEX IF NOT EXISTS idx_chain_parameters_category ON chain_parameters(category);
