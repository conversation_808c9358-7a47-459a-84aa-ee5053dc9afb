{% extends "base.html" %}

{% block title %}ONNYX Platform - The Digital Backbone of Trustworthy Commerce{% endblock %}

{% block content %}
<div class="landing-content">
<!-- Hero Section with Animated Background -->
<div class="hero-gradient cyber-grid relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Floating particles effect - Enhanced visibility -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
        <div class="absolute top-2/3 right-1/4 w-1 h-1 bg-cyber-cyan rounded-full animate-ping"></div>
        <div class="absolute top-1/2 left-1/2 w-1 h-1 bg-cyber-purple rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/3 right-1/2 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
        <div class="text-center">
            <!-- Hero Logo -->
            <div class="mb-8 flex justify-center">
                <div class="w-20 h-20 md:w-24 md:h-24 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/30 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/50 transition-all duration-500 group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-16 h-16 md:w-20 md:h-20 object-contain group-hover:scale-110 transition-all duration-500"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
            </div>

            <!-- Main Heading with Hologram Effect -->
            <h1 class="text-6xl md:text-8xl font-orbitron font-bold mb-8 fade-in">
                <span class="hologram-text">ONNYX</span>
            </h1>
            <h2 class="text-2xl md:text-4xl font-orbitron font-medium mb-6 text-cyber-cyan">
                The Digital Backbone of Trustworthy Commerce
            </h2>
            <p class="text-lg md:text-xl text-text-secondary mb-12 max-w-4xl mx-auto leading-relaxed">
                Blockchain-powered verification platform enabling transparent business operations through
                cryptographic identity management, trust scoring protocols, and decentralized validation networks.
            </p>

            <!-- Enhanced Platform Statistics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
                <div class="card text-center">
                    <div class="text-4xl md:text-5xl font-orbitron font-bold text-cyber-cyan mb-2 count-up glow-effect" data-count="{{ platform_stats.identities }}">0</div>
                    <div class="text-sm font-medium text-text-tertiary uppercase tracking-wider">Verified Identities</div>
                    <div class="w-full h-1 bg-gradient-to-r from-transparent via-cyber-cyan to-transparent mt-3"></div>
                </div>
                <div class="card text-center">
                    <div class="text-4xl md:text-5xl font-orbitron font-bold text-cyber-purple mb-2 count-up purple-glow-effect" data-count="{{ platform_stats.selas }}">0</div>
                    <div class="text-sm font-medium text-text-tertiary uppercase tracking-wider">Active Validators</div>
                    <div class="w-full h-1 bg-gradient-to-r from-transparent via-cyber-purple to-transparent mt-3"></div>
                </div>
                <div class="card text-center">
                    <div class="text-4xl md:text-5xl font-orbitron font-bold text-cyber-blue mb-2 count-up text-cyber-blue-glow" data-count="{{ platform_stats.transactions }}">0</div>
                    <div class="text-sm font-medium text-text-tertiary uppercase tracking-wider">Total Transactions</div>
                    <div class="w-full h-1 bg-gradient-to-r from-transparent via-cyber-blue to-transparent mt-3"></div>
                </div>
                <div class="card text-center">
                    <div class="text-4xl md:text-5xl font-orbitron font-bold text-green-400 mb-2 count-up" data-count="{{ platform_stats.blocks }}" style="text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);">0</div>
                    <div class="text-sm font-medium text-text-tertiary uppercase tracking-wider">Blocks Secured</div>
                    <div class="w-full h-1 bg-gradient-to-r from-transparent via-green-400 to-transparent mt-3"></div>
                </div>
            </div>

            <!-- Enhanced Call to Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a href="{{ url_for('register_choice') }}"
                   class="btn btn-primary btn-lg">
                    <span class="text-xl">🔐</span>
                    <span>Verify Identity</span>
                </a>
                <a href="{{ url_for('explorer.index') }}"
                   class="btn btn-secondary btn-lg">
                    <span class="text-xl">🔍</span>
                    <span>Launch Explorer</span>
                </a>
                <a href="{{ url_for('sela.directory') }}"
                   class="btn btn-outline btn-lg">
                    <span class="text-xl">🏢</span>
                    <span>Become a Validator</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Key Features with Cyber Theme -->
<div class="section-container py-20 relative">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="section-title text-4xl md:text-5xl text-center">
                Core Technologies
            </h2>
            <div class="section-content-buffer">
                <p class="text-xl text-text-secondary max-w-3xl mx-auto">
                    Advanced cryptographic protocols powering the future of verified business operations
                </p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Quantum-Resistant Cryptography -->
            <div class="card group">
                <div class="card-header">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center group-hover:shadow-lg group-hover:shadow-cyber-cyan/30 transition-all duration-300">
                        <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="card-title text-cyber-cyan-glow">Quantum-Resistant Security</h3>
                </div>
                <div class="card-body">
                    <p>Advanced ECDSA cryptographic identities with quantum-resistant algorithms ensuring long-term security for business operations.</p>
                </div>
                <div class="card-footer">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                        <span class="text-xs text-cyber-cyan font-mono uppercase tracking-wider">ACTIVE PROTOCOL</span>
                    </div>
                </div>
            </div>

            <!-- Etzem Trust Engine -->
            <div class="card group">
                <div class="card-header">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center group-hover:shadow-lg group-hover:shadow-cyber-purple/30 transition-all duration-300">
                        <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <h3 class="card-title text-cyber-purple-glow">Etzem Trust Protocol</h3>
                </div>
                <div class="card-body">
                    <p>Algorithmic reputation system analyzing transaction patterns, community endorsements, and business performance metrics.</p>
                </div>
                <div class="card-footer">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-cyber-purple rounded-full animate-pulse"></div>
                        <span class="text-xs text-cyber-purple font-mono uppercase tracking-wider">TRUST ENGINE</span>
                    </div>
                </div>
            </div>

            <!-- Mikvah Token System -->
            <div class="card group">
                <div class="card-header">
                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-blue to-green-400 rounded-xl flex items-center justify-center group-hover:shadow-lg group-hover:shadow-cyber-blue/30 transition-all duration-300">
                        <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="card-title text-cyber-blue-glow">Mikvah Token Economy</h3>
                </div>
                <div class="card-body">
                    <p>Decentralized token minting system enabling businesses to create loyalty programs, equity shares, and community governance tokens.</p>
                </div>
                <div class="card-footer">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-cyber-blue rounded-full animate-pulse"></div>
                        <span class="text-xs text-cyber-blue font-mono uppercase tracking-wider">TOKEN FACTORY</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Active Validators Network -->
{% if recent_selas %}
<div class="py-20 relative">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-16">
            <div>
                <h2 class="text-4xl font-orbitron font-bold mb-4">
                    <span class="hologram-text">Active Validator Network</span>
                </h2>
                <p class="text-xl text-gray-400">Verified business validators securing the network</p>
            </div>
            <a href="{{ url_for('sela.directory') }}"
               class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                View All Validators →
            </a>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for sela in recent_selas[:6] %}
            <div class="glass-card p-6 neuro-card hover:scale-105 transition-all duration-300 group data-stream">
                <div class="flex items-start justify-between mb-6">
                    <div class="flex-1">
                        <h3 class="text-xl font-orbitron font-bold text-white mb-2">
                            <a href="{{ url_for('sela.profile', sela_id=sela.sela_id) }}"
                               class="hover:text-cyber-cyan transition-colors duration-300">
                                {{ sela.name }}
                            </a>
                        </h3>
                        <p class="text-sm text-gray-400 uppercase tracking-wider">{{ sela.category }}</p>
                    </div>
                    <span class="badge-success">
                        ACTIVE
                    </span>
                </div>

                <div class="space-y-3 mb-6">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Validator ID</span>
                        <span class="text-sm font-mono text-cyber-cyan">{{ sela.sela_id[:8] }}...</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Owner</span>
                        <span class="text-sm text-gray-300">{{ sela.owner_name }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Registered</span>
                        <span class="text-sm text-gray-300">{{ sela.created_at|format_timestamp }}</span>
                    </div>
                </div>

                <div class="w-full h-1 bg-gradient-to-r from-transparent via-cyber-cyan to-transparent"></div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Blockchain Activity Stream -->
{% if recent_transactions %}
<div class="py-20 relative">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-16">
            <div>
                <h2 class="text-4xl font-orbitron font-bold mb-4">
                    <span class="hologram-text">Live Transaction Stream</span>
                </h2>
                <p class="text-xl text-gray-400">Real-time blockchain activity monitoring</p>
            </div>
            <a href="{{ url_for('explorer.transactions') }}"
               class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                Launch Explorer →
            </a>
        </div>

        <div class="glass-card neuro-card overflow-hidden">
            <div class="px-8 py-6 border-b border-white/10 bg-gradient-to-r from-cyber-cyan/10 to-cyber-purple/10">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan">Recent Transactions</h3>
            </div>
            <div class="divide-y divide-white/5">
                {% for tx in recent_transactions[:5] %}
                <div class="px-8 py-6 hover:bg-white/5 transition-all duration-300 data-stream">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <p class="text-lg font-orbitron font-semibold text-white">{{ tx.op }}</p>
                                <p class="text-sm font-mono text-cyber-cyan">{{ tx.tx_id|truncate_hash }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="badge-success">{{ tx.status|title }}</span>
                            <p class="text-sm text-gray-400 mt-2">{{ tx.created_at|format_timestamp }}</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Final Call to Action -->
<div class="py-32 relative overflow-hidden">
    <!-- Background effects - Enhanced visibility -->
    <div class="absolute inset-0 hero-gradient"></div>
    <div class="absolute inset-0 cyber-grid"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <h2 class="text-5xl md:text-7xl font-orbitron font-bold mb-8">
            <span class="hologram-text">Join the Future</span>
        </h2>
        <p class="text-2xl md:text-3xl text-cyber-cyan font-orbitron font-medium mb-6">
            of Verified Business Operations
        </p>
        <p class="text-xl text-gray-300 mb-16 max-w-4xl mx-auto leading-relaxed">
            Secure your digital identity, validate your business, and become part of the most trusted
            blockchain network for commercial operations. The future of business is transparent,
            verifiable, and decentralized.
        </p>

        <div class="flex flex-col sm:flex-row gap-8 justify-center items-center">
            <a href="{{ url_for('auth.register_identity') }}"
               class="glass-button-primary px-12 py-6 rounded-xl text-xl font-orbitron font-bold transition-all duration-300 hover:scale-110 glow-effect">
                🚀 Register Identity
            </a>
            <a href="{{ url_for('auth.register_sela') }}"
               class="glass-button px-12 py-6 rounded-xl text-xl font-orbitron font-bold transition-all duration-300 hover:scale-110">
                🏢 Register Validator
            </a>
        </div>

        <!-- Trust indicators -->
        <div class="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Quantum-Secure</h3>
                <p class="text-gray-400">Military-grade cryptographic protection</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">Lightning Fast</h3>
                <p class="text-gray-400">Instant transaction verification</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-cyber-blue to-green-400 rounded-xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-blue mb-2">Global Network</h3>
                <p class="text-gray-400">Worldwide validator infrastructure</p>
            </div>
        </div>
    </div>
</div>
</div> <!-- Close landing-content -->
{% endblock %}
