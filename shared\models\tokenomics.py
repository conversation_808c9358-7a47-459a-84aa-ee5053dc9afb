"""
Biblical Tokenomics Module

This module implements the biblical economic principles for the Onnyx blockchain,
including jubilee resets, tiered mining rewards, gleaning pools, and anti-usury lending.
"""

import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone

from shared.db.db import db
from shared.config.chain_parameters import chain_parameters

logger = logging.getLogger("onnyx.tokenomics")

class BiblicalTokenomics:
    """
    Implements biblical economic principles for the Onnyx blockchain.
    """
    
    def __init__(self):
        """Initialize the Biblical Tokenomics system."""
        self.gleaning_pool_id = "GLEANS_POOL"
        self.firstfruits_pool_id = "FIRSTFRUITS_POOL"
        self.community_pool_id = "COMMUNITY_POOL"
    
    def calculate_tiered_mining_reward(self, proposer_id: str, base_reward: float) -> Tuple[float, float]:
        """
        Calculate mining reward with deed-based multipliers and gleaning pool allocation.
        
        Args:
            proposer_id: Identity ID of the block proposer
            base_reward: Base mining reward amount
            
        Returns:
            Tuple of (effective_reward, gleaning_allocation)
        """
        try:
            # Get proposer's deed score
            deed_score = self._get_deed_score(proposer_id)
            
            # Calculate deed bonus (max 10% bonus)
            deed_multiplier = chain_parameters.get("deed_score_multiplier", 0.1)
            deed_bonus = min(deed_score * deed_multiplier, deed_multiplier)
            
            # Check for concentration penalty
            concentration_penalty = self._check_concentration_penalty(proposer_id)
            
            # Calculate effective reward
            effective_reward = base_reward * (1 + deed_bonus) * concentration_penalty
            
            # Apply min/max limits
            min_reward = chain_parameters.get("min_block_reward", 2)
            max_reward = chain_parameters.get("max_block_reward", 200)
            effective_reward = max(min_reward, min(max_reward, effective_reward))
            
            # Calculate gleaning pool allocation (2% of base reward)
            gleaning_percentage = chain_parameters.get("gleaning_pool_percentage", 0.02)
            gleaning_allocation = base_reward * gleaning_percentage
            
            logger.info(f"Calculated reward for {proposer_id}: base={base_reward}, "
                       f"deed_bonus={deed_bonus:.3f}, penalty={concentration_penalty:.3f}, "
                       f"effective={effective_reward:.3f}, gleaning={gleaning_allocation:.3f}")
            
            return effective_reward, gleaning_allocation
            
        except Exception as e:
            logger.error(f"Error calculating tiered mining reward: {e}")
            return base_reward, 0.0
    
    def _get_deed_score(self, identity_id: str) -> float:
        """Get the current deed score for an identity."""
        try:
            # Get deed score from identities table
            result = db.query_one(
                "SELECT deeds_score FROM identities WHERE identity_id = ?",
                (identity_id,)
            )
            return result["deeds_score"] if result else 0.0
        except Exception as e:
            logger.warning(f"Could not get deed score for {identity_id}: {e}")
            return 0.0
    
    def _check_concentration_penalty(self, identity_id: str) -> float:
        """Check if identity should receive concentration penalty."""
        try:
            # Get total balance for identity
            result = db.query_one("""
                SELECT SUM(balance) as total_balance 
                FROM token_balances 
                WHERE identity_id = ? AND token_id = 'ONX'
            """, (identity_id,))
            
            total_balance = result["total_balance"] if result and result["total_balance"] else 0
            concentration_threshold = chain_parameters.get("concentration_threshold", 1000000)
            
            if total_balance > concentration_threshold:
                penalty_rate = chain_parameters.get("concentration_penalty_rate", 0.1)
                logger.info(f"Applying concentration penalty to {identity_id}: "
                           f"balance={total_balance}, threshold={concentration_threshold}")
                return penalty_rate
            
            return 1.0
            
        except Exception as e:
            logger.warning(f"Could not check concentration penalty for {identity_id}: {e}")
            return 1.0
    
    def allocate_to_gleaning_pool(self, amount: float, token_id: str = "ONX") -> bool:
        """
        Allocate tokens to the gleaning pool.
        
        Args:
            amount: Amount to allocate
            token_id: Token ID (default: ONX)
            
        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())
            
            # Update gleaning pool balance
            db.execute("""
                INSERT OR REPLACE INTO jubilee_pools (pool_id, pool_type, total_amount, token_id, created_at, last_distribution)
                VALUES (?, 'GLEANING', COALESCE((SELECT total_amount FROM jubilee_pools WHERE pool_id = ?), 0) + ?, ?, ?, ?)
            """, (self.gleaning_pool_id, self.gleaning_pool_id, amount, token_id, current_time, current_time))
            
            logger.info(f"Allocated {amount} {token_id} to gleaning pool")
            return True
            
        except Exception as e:
            logger.error(f"Error allocating to gleaning pool: {e}")
            return False
    
    def record_deed(self, identity_id: str, deed_type: str, deed_value: float, 
                   description: str = "", block_height: int = None) -> bool:
        """
        Record a righteous deed for an identity.
        
        Args:
            identity_id: Identity ID
            deed_type: Type of deed (MUTUAL_AID, DONATION, FIRSTFRUITS, SABBATH_OBSERVANCE)
            deed_value: Value/score of the deed
            description: Optional description
            block_height: Current block height
            
        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())
            
            # Record the deed
            db.execute("""
                INSERT INTO deeds_ledger (identity_id, deed_type, deed_value, description, timestamp, block_height)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (identity_id, deed_type, deed_value, description, current_time, block_height))
            
            # Update identity's deed score
            self._update_deed_score(identity_id, deed_value)
            
            logger.info(f"Recorded deed for {identity_id}: {deed_type} = {deed_value}")
            return True
            
        except Exception as e:
            logger.error(f"Error recording deed: {e}")
            return False
    
    def _update_deed_score(self, identity_id: str, deed_value: float) -> None:
        """Update an identity's deed score."""
        try:
            db.execute("""
                UPDATE identities 
                SET deeds_score = COALESCE(deeds_score, 0) + ?, 
                    updated_at = ?
                WHERE identity_id = ?
            """, (deed_value, int(time.time()), identity_id))
        except Exception as e:
            logger.warning(f"Could not update deed score for {identity_id}: {e}")
    
    def update_activity_tracking(self, identity_id: str, block_height: int) -> None:
        """
        Update activity tracking for an identity.
        
        Args:
            identity_id: Identity ID
            block_height: Current block height
        """
        try:
            current_time = int(time.time())
            
            db.execute("""
                UPDATE identities 
                SET last_active_timestamp = ?, 
                    last_transaction_height = ?,
                    updated_at = ?
                WHERE identity_id = ?
            """, (current_time, block_height, current_time, identity_id))
            
        except Exception as e:
            logger.warning(f"Could not update activity tracking for {identity_id}: {e}")
    
    def check_dormant_accounts(self, current_block_height: int) -> List[str]:
        """
        Check for dormant accounts that should be subject to jubilee reclamation.
        
        Args:
            current_block_height: Current blockchain height
            
        Returns:
            List of dormant identity IDs
        """
        try:
            dormancy_threshold = chain_parameters.get("dormancy_threshold_blocks", 7200)
            threshold_height = current_block_height - dormancy_threshold
            
            dormant_accounts = db.query("""
                SELECT identity_id, last_transaction_height, last_active_timestamp
                FROM identities 
                WHERE last_transaction_height < ? AND last_transaction_height > 0
            """, (threshold_height,))
            
            dormant_ids = [acc["identity_id"] for acc in dormant_accounts]
            
            if dormant_ids:
                logger.info(f"Found {len(dormant_ids)} dormant accounts at block {current_block_height}")
            
            return dormant_ids
            
        except Exception as e:
            logger.error(f"Error checking dormant accounts: {e}")
            return []
    
    def is_sabbath_period(self) -> bool:
        """
        Check if current time is within Sabbath period.
        
        Returns:
            True if it's currently Sabbath
        """
        try:
            now = datetime.now(timezone.utc)
            
            # Get Sabbath configuration
            sabbath_start_day = chain_parameters.get("sabbath_start_day", 5)  # Friday
            sabbath_start_hour = chain_parameters.get("sabbath_start_hour", 18)  # 6 PM
            sabbath_duration = chain_parameters.get("sabbath_duration_hours", 25)  # 25 hours
            
            # Calculate if we're in Sabbath period
            # This is a simplified implementation - in production you'd want proper timezone handling
            current_day = now.weekday()  # 0=Monday, 6=Sunday
            current_hour = now.hour
            
            if current_day == sabbath_start_day and current_hour >= sabbath_start_hour:
                return True
            elif current_day == (sabbath_start_day + 1) % 7 and current_hour < (sabbath_start_hour + sabbath_duration - 24):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking Sabbath period: {e}")
            return False

# Global instance
biblical_tokenomics = BiblicalTokenomics()
