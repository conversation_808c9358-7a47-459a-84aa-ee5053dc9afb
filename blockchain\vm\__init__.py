"""
Onnyx Virtual Machine (VM) Module

This module implements the Onnyx Virtual Machine, which is responsible for
validating and executing transactions on the Onnyx blockchain.

The VM uses a stack-based architecture with opcodes that represent different
operations that can be performed on the blockchain, such as minting tokens,
transferring tokens, registering identities, and submitting governance proposals.
"""

from .opcodes import (
    OP_MINT,
    OP_SEND,
    OP_IDENTITY,
    OP_SCROLL,
    OP_BURN,
    OP_GRANT_REPUTATION,
    OP_STAKE,
    OP_VOTE,
    OP_REWARD,
    op_mint,
    op_send,
    op_identity,
    op_scroll,
    op_burn,
    op_grant_reputation,
    op_stake,
    op_vote,
    op_reward,
    execute_opcode,
    OpcodeError,
    OpcodeMintError,
    OpcodeSendError,
    OpcodeIdentityError,
    OpcodeScrollError,
    OpcodeBurnError,
    OpcodeGrantReputationError,
    OpcodeStakeError,
    OpcodeVoteError,
    OpcodeRewardError
)

from .vm import OnnyxVM
from .validator import (
    validate_transaction,
    validate_transaction_safe,
    validate_transactions,
    ValidationError,
    format_validation_error,
    get_transaction_type,
    summarize_transaction
)

from .block_validator import (
    validate_block,
    validate_block_safe,
    validate_chain,
    validate_block_structure,
    validate_block_hash,
    validate_block_transactions,
    validate_block_miner_reward,
    validate_block_against_chain,
    BlockValidationError
)

__all__ = [
    # Opcodes
    'OP_MINT',
    'OP_SEND',
    'OP_IDENTITY',
    'OP_SCROLL',
    'OP_BURN',
    'OP_GRANT_REPUTATION',
    'OP_STAKE',
    'OP_VOTE',
    'OP_REWARD',
    'op_mint',
    'op_send',
    'op_identity',
    'op_scroll',
    'op_burn',
    'op_grant_reputation',
    'op_stake',
    'op_vote',
    'op_reward',
    'execute_opcode',
    'OpcodeError',
    'OpcodeMintError',
    'OpcodeSendError',
    'OpcodeIdentityError',
    'OpcodeScrollError',
    'OpcodeBurnError',
    'OpcodeGrantReputationError',
    'OpcodeStakeError',
    'OpcodeVoteError',
    'OpcodeRewardError',

    # VM
    'OnnyxVM',

    # Transaction Validator
    'validate_transaction',
    'validate_transaction_safe',
    'validate_transactions',
    'ValidationError',
    'format_validation_error',
    'get_transaction_type',
    'summarize_transaction',

    # Block Validator
    'validate_block',
    'validate_block_safe',
    'validate_chain',
    'validate_block_structure',
    'validate_block_hash',
    'validate_block_transactions',
    'validate_block_miner_reward',
    'validate_block_against_chain',
    'BlockValidationError'
]
