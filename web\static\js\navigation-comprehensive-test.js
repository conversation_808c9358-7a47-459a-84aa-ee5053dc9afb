/**
 * ONNYX Navigation - Comprehensive Test Suite
 * Tests all navigation features including mobile menu, active states, and responsiveness
 */

class NavigationComprehensiveTest {
    constructor() {
        this.testResults = {
            mobileMenu: {},
            activeStates: {},
            responsiveness: {},
            accessibility: {},
            cyberpunkTheme: {}
        };
        this.init();
    }

    init() {
        console.log('🧪 ONNYX Navigation Comprehensive Test Suite Starting...');
        this.runAllTests();
    }

    async runAllTests() {
        console.log('📋 Running comprehensive navigation tests...');
        
        // Test 1: Mobile Menu Functionality
        await this.testMobileMenuFunctionality();
        
        // Test 2: Active State Detection
        await this.testActiveStateDetection();
        
        // Test 3: Responsive Design
        await this.testResponsiveDesign();
        
        // Test 4: Accessibility Features
        await this.testAccessibilityFeatures();
        
        // Test 5: Cyberpunk Theme Consistency
        await this.testCyberpunkTheme();
        
        // Test 6: Cross-browser Compatibility
        await this.testCrossBrowserCompatibility();
        
        this.generateFinalReport();
    }

    async testMobileMenuFunctionality() {
        console.log('📱 Testing mobile menu functionality...');
        
        const mobileBtn = document.getElementById('mobile-menu-button');
        const mobileOverlay = document.getElementById('mobile-menu-overlay');
        const hamburger = document.getElementById('hamburger-icon');
        
        this.testResults.mobileMenu = {
            elementsPresent: !!(mobileBtn && mobileOverlay && hamburger),
            buttonClickable: false,
            overlayAnimates: false,
            hamburgerTransforms: false,
            closeOnEscape: false,
            closeOnOutsideClick: false,
            ariaAttributes: false
        };
        
        if (mobileBtn && mobileOverlay && hamburger) {
            // Test button click
            try {
                mobileBtn.click();
                await this.delay(500);
                
                const isOpen = mobileOverlay.style.display !== 'none' && mobileOverlay.style.opacity === '1';
                this.testResults.mobileMenu.buttonClickable = isOpen;
                this.testResults.mobileMenu.overlayAnimates = isOpen;
                this.testResults.mobileMenu.hamburgerTransforms = hamburger.classList.contains('active');
                
                // Test ARIA attributes
                this.testResults.mobileMenu.ariaAttributes = 
                    mobileBtn.getAttribute('aria-expanded') === 'true' &&
                    mobileOverlay.getAttribute('aria-hidden') === 'false';
                
                // Test escape key
                const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
                document.dispatchEvent(escapeEvent);
                await this.delay(500);
                
                this.testResults.mobileMenu.closeOnEscape = 
                    mobileOverlay.style.display === 'none' || mobileOverlay.style.opacity === '0';
                
            } catch (error) {
                console.error('Mobile menu test error:', error);
            }
        }
        
        console.log('✅ Mobile menu functionality test completed');
    }

    async testActiveStateDetection() {
        console.log('🎯 Testing active state detection...');
        
        const currentPath = window.location.pathname;
        let expectedActiveRoute = 'index';
        
        if (currentPath.startsWith('/tokenomics')) expectedActiveRoute = 'tokenomics';
        else if (currentPath.startsWith('/dashboard')) expectedActiveRoute = 'dashboard';
        else if (currentPath.startsWith('/explorer')) expectedActiveRoute = 'explorer';
        else if (currentPath.startsWith('/sela')) expectedActiveRoute = 'sela';
        else if (currentPath.startsWith('/auto_mining')) expectedActiveRoute = 'auto_mining';
        
        const desktopActiveItem = document.querySelector(`.navbar-nav .nav-item[data-route="${expectedActiveRoute}"].nav-item-active`);
        const mobileActiveItem = document.querySelector(`.mobile-nav-item[data-route="${expectedActiveRoute}"].mobile-nav-item-active`);
        
        this.testResults.activeStates = {
            routeDetected: expectedActiveRoute,
            desktopActiveCorrect: !!desktopActiveItem,
            mobileActiveCorrect: !!mobileActiveItem,
            activeStylesApplied: false,
            featuredItemHandled: false
        };
        
        // Test active styles
        if (desktopActiveItem) {
            const styles = window.getComputedStyle(desktopActiveItem);
            this.testResults.activeStates.activeStylesApplied = 
                styles.background.includes('gradient') || styles.background.includes('linear-gradient');
        }
        
        // Test featured item handling
        const featuredItem = document.querySelector('.nav-item-featured');
        if (featuredItem && expectedActiveRoute === 'tokenomics') {
            this.testResults.activeStates.featuredItemHandled = 
                featuredItem.classList.contains('nav-item-active');
        } else {
            this.testResults.activeStates.featuredItemHandled = true; // Not applicable
        }
        
        console.log('✅ Active state detection test completed');
    }

    async testResponsiveDesign() {
        console.log('📐 Testing responsive design...');
        
        const breakpoints = [
            { name: 'Mobile', width: 480 },
            { name: 'Tablet', width: 768 },
            { name: 'Desktop', width: 1200 }
        ];
        
        this.testResults.responsiveness = {};
        
        for (const breakpoint of breakpoints) {
            await this.testBreakpoint(breakpoint);
        }
        
        console.log('✅ Responsive design test completed');
    }

    async testBreakpoint(breakpoint) {
        // Simulate viewport
        this.simulateViewport(breakpoint.width);
        await this.delay(300);
        
        const navbar = document.querySelector('.navbar-nav');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        
        let navVisible, mobileVisible;
        
        if (breakpoint.width <= 768) {
            navVisible = navbar && window.getComputedStyle(navbar).display !== 'none';
            mobileVisible = mobileToggle && window.getComputedStyle(mobileToggle).display !== 'none';
            
            this.testResults.responsiveness[breakpoint.name] = {
                navHidden: !navVisible,
                mobileToggleVisible: mobileVisible,
                touchTargetsValid: this.validateTouchTargets(),
                passed: !navVisible && mobileVisible
            };
        } else {
            navVisible = navbar && window.getComputedStyle(navbar).display !== 'none';
            mobileVisible = mobileToggle && window.getComputedStyle(mobileToggle).display !== 'none';
            
            this.testResults.responsiveness[breakpoint.name] = {
                navVisible: navVisible,
                mobileToggleHidden: !mobileVisible,
                touchTargetsValid: this.validateTouchTargets(),
                passed: navVisible && !mobileVisible
            };
        }
    }

    validateTouchTargets() {
        const targets = document.querySelectorAll('.nav-item, .auth-btn, .user-button, .mobile-menu-btn, .mobile-nav-item');
        let validTargets = 0;
        let totalVisibleTargets = 0;
        
        targets.forEach(target => {
            const styles = window.getComputedStyle(target);
            if (styles.display !== 'none' && styles.visibility !== 'hidden') {
                totalVisibleTargets++;
                const rect = target.getBoundingClientRect();
                if (rect.height >= 44 && rect.width >= 44) {
                    validTargets++;
                }
            }
        });
        
        return totalVisibleTargets === 0 || validTargets === totalVisibleTargets;
    }

    async testAccessibilityFeatures() {
        console.log('♿ Testing accessibility features...');
        
        const mobileBtn = document.getElementById('mobile-menu-button');
        const mobileOverlay = document.getElementById('mobile-menu-overlay');
        
        this.testResults.accessibility = {
            ariaLabels: false,
            keyboardNavigation: false,
            focusManagement: false,
            semanticHTML: false
        };
        
        // Test ARIA labels
        if (mobileBtn) {
            this.testResults.accessibility.ariaLabels = 
                mobileBtn.hasAttribute('aria-label') &&
                mobileBtn.hasAttribute('aria-expanded');
        }
        
        // Test semantic HTML
        const nav = document.querySelector('nav');
        const buttons = document.querySelectorAll('button');
        this.testResults.accessibility.semanticHTML = !!(nav && buttons.length > 0);
        
        // Test keyboard navigation (escape key)
        try {
            if (mobileBtn) {
                mobileBtn.click();
                await this.delay(300);
                
                const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
                document.dispatchEvent(escapeEvent);
                await this.delay(300);
                
                this.testResults.accessibility.keyboardNavigation = 
                    mobileOverlay.style.display === 'none' || mobileOverlay.style.opacity === '0';
            }
        } catch (error) {
            console.error('Accessibility test error:', error);
        }
        
        console.log('✅ Accessibility features test completed');
    }

    async testCyberpunkTheme() {
        console.log('🎨 Testing cyberpunk theme consistency...');
        
        const navbar = document.querySelector('.onnyx-navbar');
        const featuredItem = document.querySelector('.nav-item-featured');
        const mobileOverlay = document.querySelector('.mobile-menu-overlay');
        
        this.testResults.cyberpunkTheme = {
            glassMorphism: false,
            cyberColors: false,
            gradientBackgrounds: false,
            glowEffects: false
        };
        
        if (navbar) {
            const navStyles = window.getComputedStyle(navbar);
            this.testResults.cyberpunkTheme.glassMorphism = 
                navStyles.backdropFilter.includes('blur') || navStyles.webkitBackdropFilter.includes('blur');
            this.testResults.cyberpunkTheme.gradientBackgrounds = 
                navStyles.background.includes('gradient');
        }
        
        if (featuredItem) {
            const featuredStyles = window.getComputedStyle(featuredItem);
            this.testResults.cyberpunkTheme.cyberColors = 
                featuredStyles.borderColor.includes('255') || featuredStyles.color.includes('255');
            this.testResults.cyberpunkTheme.glowEffects = 
                featuredStyles.boxShadow !== 'none';
        }
        
        console.log('✅ Cyberpunk theme test completed');
    }

    async testCrossBrowserCompatibility() {
        console.log('🌐 Testing cross-browser compatibility...');
        
        // Test CSS features support
        const supportsBackdropFilter = CSS.supports('backdrop-filter', 'blur(10px)') || 
                                      CSS.supports('-webkit-backdrop-filter', 'blur(10px)');
        const supportsFlexbox = CSS.supports('display', 'flex');
        const supportsGrid = CSS.supports('display', 'grid');
        
        console.log(`Backdrop filter support: ${supportsBackdropFilter}`);
        console.log(`Flexbox support: ${supportsFlexbox}`);
        console.log(`Grid support: ${supportsGrid}`);
        
        console.log('✅ Cross-browser compatibility test completed');
    }

    simulateViewport(width) {
        const style = document.createElement('style');
        style.id = 'viewport-test-simulator';
        style.textContent = `
            @media (min-width: ${width + 1}px) {
                .viewport-test { display: none; }
            }
            @media (max-width: ${width}px) {
                .viewport-test { display: block; }
            }
        `;
        
        const existing = document.getElementById('viewport-test-simulator');
        if (existing) existing.remove();
        
        document.head.appendChild(style);
    }

    generateFinalReport() {
        console.log('\n🎯 COMPREHENSIVE NAVIGATION TEST REPORT');
        console.log('='.repeat(50));
        
        // Mobile Menu Results
        const mobileMenuPassed = Object.values(this.testResults.mobileMenu).filter(Boolean).length;
        const mobileMenuTotal = Object.keys(this.testResults.mobileMenu).length;
        console.log(`📱 Mobile Menu: ${mobileMenuPassed}/${mobileMenuTotal} tests passed`);
        
        // Active States Results
        const activeStatesPassed = Object.values(this.testResults.activeStates).filter(Boolean).length;
        const activeStatesTotal = Object.keys(this.testResults.activeStates).length;
        console.log(`🎯 Active States: ${activeStatesPassed}/${activeStatesTotal} tests passed`);
        
        // Responsiveness Results
        const responsivenessPassed = Object.values(this.testResults.responsiveness).filter(r => r.passed).length;
        const responsivenessTotal = Object.keys(this.testResults.responsiveness).length;
        console.log(`📐 Responsiveness: ${responsivenessPassed}/${responsivenessTotal} breakpoints passed`);
        
        // Accessibility Results
        const accessibilityPassed = Object.values(this.testResults.accessibility).filter(Boolean).length;
        const accessibilityTotal = Object.keys(this.testResults.accessibility).length;
        console.log(`♿ Accessibility: ${accessibilityPassed}/${accessibilityTotal} tests passed`);
        
        // Cyberpunk Theme Results
        const themePassed = Object.values(this.testResults.cyberpunkTheme).filter(Boolean).length;
        const themeTotal = Object.keys(this.testResults.cyberpunkTheme).length;
        console.log(`🎨 Cyberpunk Theme: ${themePassed}/${themeTotal} tests passed`);
        
        const overallPassed = mobileMenuPassed + activeStatesPassed + responsivenessPassed + accessibilityPassed + themePassed;
        const overallTotal = mobileMenuTotal + activeStatesTotal + responsivenessTotal + accessibilityTotal + themeTotal;
        const overallPercentage = ((overallPassed / overallTotal) * 100).toFixed(1);
        
        console.log(`\n🏆 OVERALL SCORE: ${overallPassed}/${overallTotal} (${overallPercentage}%)`);
        
        if (overallPercentage >= 90) {
            console.log('🎉 EXCELLENT! Navigation system is production-ready.');
        } else if (overallPercentage >= 75) {
            console.log('✅ GOOD! Minor improvements needed.');
        } else {
            console.log('⚠️ NEEDS WORK! Several issues require attention.');
        }
        
        // Display results in UI
        this.displayResultsInUI(overallPercentage, overallPassed, overallTotal);
    }

    displayResultsInUI(percentage, passed, total) {
        const resultDiv = document.createElement('div');
        resultDiv.id = 'navigation-test-results';
        resultDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${percentage >= 90 ? 'rgba(34, 197, 94, 0.9)' : percentage >= 75 ? 'rgba(251, 191, 36, 0.9)' : 'rgba(239, 68, 68, 0.9)'};
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
            z-index: 10001;
            max-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;

        resultDiv.innerHTML = `
            <strong>🧪 Navigation Test Results</strong><br>
            Score: ${percentage}% (${passed}/${total})<br>
            Status: ${percentage >= 90 ? '🎉 EXCELLENT' : percentage >= 75 ? '✅ GOOD' : '⚠️ NEEDS WORK'}
        `;

        const existing = document.getElementById('navigation-test-results');
        if (existing) existing.remove();

        document.body.appendChild(resultDiv);

        setTimeout(() => resultDiv.remove(), 15000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Auto-run comprehensive test
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            window.navigationComprehensiveTest = new NavigationComprehensiveTest();
        }, 2000); // Wait for navigation system to initialize
    });
} else {
    setTimeout(() => {
        window.navigationComprehensiveTest = new NavigationComprehensiveTest();
    }, 2000);
}
